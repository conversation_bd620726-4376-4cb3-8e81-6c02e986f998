import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Chip,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  Refresh,
  GetApp,
  Clear,
  FullscreenExit,
  Fullscreen
} from '@mui/icons-material';
import { format } from 'date-fns';

interface ProcessInfo {
  task_id: string;
  process_type: string;
  status: string;
  start_time: string;
}

interface RealTimeLogViewerProps {
  processes: ProcessInfo[];
}

const RealTimeLogViewer: React.FC<RealTimeLogViewerProps> = ({ processes = [] }) => {
  const [selectedTaskId, setSelectedTaskId] = useState<string>('');
  const [logs, setLogs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [refreshInterval] = useState(3000); // 3 seconds
  const [maxLines, setMaxLines] = useState(100);
  
  const logContainerRef = useRef<HTMLDivElement>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const autoScrollRef = useRef(true);

  // Log processes and selectedTaskId changes for debugging
  useEffect(() => {
    console.log('RealTimeLogViewer: processes prop updated:', JSON.stringify(processes, null, 2));
  }, [processes]);

  useEffect(() => {
    console.log('RealTimeLogViewer: selectedTaskId changed:', selectedTaskId);
  }, [selectedTaskId]);

  // Auto-scroll to bottom when new logs arrive
  const scrollToBottom = useCallback(() => {
    if (autoScrollRef.current && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, []);
  // Fetch log tail for selected process
  const fetchLogTail = useCallback(async () => {
    if (!selectedTaskId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/v1/processes/${selectedTaskId}/log-tail?lines=${maxLines}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch logs: ${response.statusText}`);
      }
      const data = await response.json();
      
      if (data.logs) { // Changed from data.lines to data.logs
        setLogs(data.logs); // Changed from data.lines to data.logs
        setError(null);
        
        // Scroll to bottom after a short delay to ensure DOM update
        setTimeout(scrollToBottom, 100);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  }, [selectedTaskId, maxLines, scrollToBottom]);

  // Set up auto-refresh
  useEffect(() => {
    if (autoRefresh && selectedTaskId) {
      refreshIntervalRef.current = setInterval(fetchLogTail, refreshInterval);
    } else {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, selectedTaskId, refreshInterval, fetchLogTail]);

  // Initial fetch when task ID changes
  useEffect(() => {
    if (selectedTaskId) {
      fetchLogTail();
    } else {
      setLogs([]);
    }
  }, [selectedTaskId, fetchLogTail]);
  // Auto-select first running process
  useEffect(() => {
    if (!selectedTaskId && processes && processes.length > 0) {
      // Filter out processes with invalid task_ids to avoid ghost process issues
      const validProcesses = processes.filter(p => p && p.task_id && typeof p.task_id === 'string' && p.task_id.trim() !== '' && p.task_id !== 'undefined' && p.task_id !== '(...)');
      if (validProcesses.length === 0) {
        console.log('RealTimeLogViewer: No valid processes to auto-select.');
        return;
      }
      const runningProcess = validProcesses.find(p => p.status === 'running');
      if (runningProcess && runningProcess.task_id) {
        console.log('RealTimeLogViewer: Auto-selecting running process:', runningProcess.task_id);
        setSelectedTaskId(runningProcess.task_id);
      } else if (validProcesses[0] && validProcesses[0].task_id) {
        console.log('RealTimeLogViewer: Auto-selecting first valid process:', validProcesses[0].task_id);
        setSelectedTaskId(validProcesses[0].task_id);
      }
    }
  }, [processes, selectedTaskId]);

  // Reset selectedTaskId if current process is no longer valid or processes list is empty
  useEffect(() => {
    if (selectedTaskId) {
      if (!processes || processes.length === 0) {
        console.log(`RealTimeLogViewer: Processes list is empty, clearing selection for ${selectedTaskId}`);
        setSelectedTaskId('');
        setLogs([]);
        setError(null);
        return;
      }
      const currentProcess = processes.find(p => p && p.task_id === selectedTaskId);
      if (!currentProcess || !currentProcess.task_id || typeof currentProcess.task_id !== 'string' || currentProcess.task_id.trim() === '' || currentProcess.task_id === 'undefined' || currentProcess.task_id === '(...)') {
        console.log(`RealTimeLogViewer: Current selected process ${selectedTaskId} is no longer valid or found, clearing selection`);
        setSelectedTaskId('');
        setLogs([]);
        setError(null);
      }
    }
  }, [processes, selectedTaskId]);

  const handleTaskIdChange = (taskId: string) => {
    // Ensure taskId is a string, even if an unexpected value comes from the event
    const newTaskId = typeof taskId === 'string' ? taskId : '';
    console.log(`RealTimeLogViewer: handleTaskIdChange - newTaskId: ${newTaskId}`);
    setSelectedTaskId(newTaskId);
    if (newTaskId) {
      setLogs([]); // Clear logs for the new selection before fetching
      setError(null);
      // fetchLogTail will be called by the useEffect watching selectedTaskId
    } else {
      setLogs([]);
      setError(null);
    }
  };

  const handleRefresh = () => {
    fetchLogTail();
  };

  const handleClearLogs = () => {
    setLogs([]);
  };
  const handleDownloadLogs = async () => {
    if (!selectedTaskId) return;

    try {
      const response = await fetch(`/api/v1/processes/${selectedTaskId}/log-full`);
      if (!response.ok) {
        throw new Error(`Failed to fetch full logs: ${response.statusText}`);
      }
      const data = await response.json();
      console.log('Full log content received from backend:', data.content); // DEBUG LINE
      
      if (data.content) {
        const blob = new Blob([data.content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${selectedTaskId}_full.log`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download logs');
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  const getStatusColor = (status: string | undefined) => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'running':
        return 'primary';
      case 'completed':
        return 'success';
      case 'failed':
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatLogLine = (line: string, index: number) => {
    // Simple log parsing for colored output
    const isError = line.toLowerCase().includes('error') || line.toLowerCase().includes('exception');
    const isWarning = line.toLowerCase().includes('warning') || line.toLowerCase().includes('warn');
    const isInfo = line.toLowerCase().includes('info');
    
    let color = 'text.primary';
    if (isError) color = 'error.main';
    else if (isWarning) color = 'warning.main';
    else if (isInfo) color = 'info.main';

    return (
      <Box
        key={index}
        component="div"
        sx={{
          fontFamily: 'monospace',
          fontSize: '0.75rem',
          color: color,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          borderLeft: isError ? '3px solid' : 'none',
          borderColor: 'error.main',
          pl: isError ? 1 : 0,
          py: 0.25,
          '&:hover': {
            backgroundColor: 'action.hover'
          }
        }}
      >
        {line}
      </Box>
    );
  };
  const selectedProcess = processes && processes.length > 0 
    ? processes.find(p => p && p.task_id === selectedTaskId && p.task_id !== 'undefined' && p.task_id !== '(...)') 
    : undefined;

  return (
    <Card
      elevation={isFullscreen ? 4 : 1}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: isFullscreen ? '100vh' : 'calc(100vh - 120px)', // Use viewport height minus header/padding
        position: isFullscreen ? 'fixed' : 'relative',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: isFullscreen ? 1300 : 'auto'
      }}
    >
      <CardHeader
        title="Real-time Process Logs"
        subheader={selectedProcess ? `${selectedProcess.process_type} - ${selectedProcess.status}` : 'No process selected'}
        action={
          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={toggleFullscreen} size="small">
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        }
      />
      
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', p: isFullscreen ? 2 : 1, '&:last-child': { pb: isFullscreen ? 2 : 1 } }}>
        {/* Controls */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth size="small">
              <InputLabel>Select Process</InputLabel>
              <Select
                value={selectedTaskId || ''} // Ensure value is always a string, default to empty if null/undefined
                onChange={(e) => handleTaskIdChange(e.target.value)}
                label="Select Process"
                displayEmpty // Allows the label to be shown when value is empty
              >
                <MenuItem value="" disabled>
                  <em>Select a process</em>
                </MenuItem>
                {(processes || [])
                  .filter(process => process && process.task_id && typeof process.task_id === 'string' && process.task_id !== 'undefined' && process.task_id !== '(...)' && process.task_id.trim() !== '')
                  .map((process) => {
                    // Ensure process and task_id are valid before rendering MenuItem
                    if (!process || !process.task_id || typeof process.task_id !== 'string' || process.task_id.trim() === '') {
                      console.warn('RealTimeLogViewer: Skipping rendering of MenuItem due to invalid process or task_id:', process);
                      return null; // Skip rendering this item if data is invalid
                    }
                    return (
                      <MenuItem key={process.task_id} value={process.task_id}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                          <Typography variant="body2">
                            {process.process_type || 'Unknown Type'} ({process.task_id ? process.task_id.slice(-8) : 'N/A'})
                          </Typography>
                          <Chip 
                            label={process.status || 'Unknown'}
                            color={getStatusColor(process.status)}
                            size="small"
                          />
                        </Box>
                      </MenuItem>
                    );
                  })}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Lines</InputLabel>
              <Select
                value={maxLines}
                onChange={(e) => setMaxLines(Number(e.target.value))}
                label="Lines"
              >
                <MenuItem value={50}>50</MenuItem>
                <MenuItem value={100}>100</MenuItem>
                <MenuItem value={200}>200</MenuItem>
                <MenuItem value={500}>500</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" gap={1}>
              <FormControlLabel
                control={
                  <Switch
                    checked={autoRefresh}
                    onChange={toggleAutoRefresh}
                    disabled={!selectedTaskId}
                    size="small"
                  />
                }
                label="Auto-refresh"
              />
              
              <Tooltip title="Refresh">
                <span>
                  <IconButton onClick={handleRefresh} size="small" disabled={!selectedTaskId}>
                    <Refresh />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title="Clear View">
                <IconButton onClick={handleClearLogs} size="small">
                  <Clear />
                </IconButton>
              </Tooltip>

              <Tooltip title="Download Full Log">
                <span>
                  <IconButton onClick={handleDownloadLogs} size="small" disabled={!selectedTaskId}>
                    <GetApp />
                  </IconButton>
                </span>
              </Tooltip>

              {loading && <CircularProgress size={20} />}
            </Box>
          </Grid>
        </Grid>

        {/* Status Info */}
        {selectedProcess && (
          <Box sx={{ mb: 2 }}>
            <Paper variant="outlined" sx={{ p: 1.5 }}>
              <Grid container spacing={2}>
                <Grid item xs={6} md={3}>
                  <Typography variant="caption" color="text.secondary">Process Type</Typography>
                  <Typography variant="body2">{selectedProcess.process_type || 'Unknown'}</Typography>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Typography variant="caption" color="text.secondary">Status</Typography>
                  <Box>
                    <Chip 
                      label={selectedProcess.status || 'Unknown'} 
                      color={getStatusColor(selectedProcess.status)}
                      size="small"
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Typography variant="caption" color="text.secondary">Started</Typography>
                  <Typography variant="body2">
                    {selectedProcess.start_time 
                      ? format(new Date(selectedProcess.start_time), 'PPpp') 
                      : 'Unknown'}
                  </Typography>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Typography variant="caption" color="text.secondary">Auto-refresh</Typography>
                  <Typography variant="body2">
                    {autoRefresh ? `Every ${refreshInterval / 1000}s` : 'Disabled'}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Log Content */}
        <Box
          ref={logContainerRef}
          sx={{
            flexGrow: 1, // Ensure it takes all available space
            minHeight: '200px',
            maxHeight: '100%', // Allow it to use full available height
            overflowY: 'auto',
            p: 1.5, // Adjusted padding
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            backgroundColor: '#f0f0f0', // Explicit light grey background
            color: '#333333', // Explicit dark text color
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'action.selected',
              borderRadius: '4px',
            },
            '&:hover::-webkit-scrollbar-thumb': {
              backgroundColor: 'action.hover',
            },
          }}
          onScroll={(e) => {
            const target = e.target as HTMLDivElement;
            const isScrolledToBottom = target.scrollHeight - target.scrollTop === target.clientHeight;
            autoScrollRef.current = isScrolledToBottom;
          }}
        >
          {loading && logs.length === 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <CircularProgress />
            </Box>
          )}
          {!loading && !selectedTaskId && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography variant="body1" sx={{ color: '#555555' }}>
                Select a process to view logs
              </Typography>
            </Box>
          )}
          {!loading && selectedTaskId && logs.length === 0 && !error && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography variant="body1" sx={{ color: '#555555' }}>
                No logs available for this process.
              </Typography>
            </Box>
          )}
          {error && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p:2 }}>
              <Alert severity="error" sx={{width: '100%'}}>Error: {error}</Alert>
            </Box>
          )}
          {logs.map((line, index) => formatLogLine(line, index))}
        </Box>

        {/* Footer with log stats */}
        <Divider sx={{ my: 1 }} />
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="caption" color="text.secondary">
            {logs.length} lines displayed
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Last updated: {new Date().toLocaleTimeString()}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RealTimeLogViewer;
