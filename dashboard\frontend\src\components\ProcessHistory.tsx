import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogContent,
  DialogTitle,
  TablePagination
} from '@mui/material';
import { Visibility, Refresh, GetApp } from '@mui/icons-material';
import { format } from 'date-fns';
import LogViewer from './LogViewer';
import { calculateDuration } from '../utils/duration';

interface ProcessHistoryItem {
  task_id: string;
  process_type: string;
  status: string;
  start_time: string; // Changed from started_at
  end_time?: string;   // Changed from completed_at
  parameters: Record<string, any>;
  message?: string;
  error?: string;
  updated_at?: string;
  duration_seconds?: number; // Added for duration calculation
}

const ProcessHistory: React.FC = () => {
  const [history, setHistory] = useState<ProcessHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const fetchHistory = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/processes/history');
      if (!response.ok) {
        throw new Error(`Failed to fetch process history: ${response.statusText}`);
      }
      let data = await response.json();
      // Frontend data transformation: map backend fields to frontend interface
      // Ensure end_time is used, and fallback to updated_at if end_time is missing for terminal states
      data = data.map((item: any) => {
        const isTerminal = item.status === 'completed' || item.status === 'failed' || item.status === 'error' || item.status === 'cancelled';
        let effectiveEndTime = item.end_time;
        if (!effectiveEndTime && item.completed_at) {
          effectiveEndTime = item.completed_at;
        }
        if (isTerminal && !effectiveEndTime && item.updated_at) {
          console.warn(`ProcessHistory: Process ${item.task_id} (${item.status}) missing end_time, using updated_at as fallback.`);
          effectiveEndTime = item.updated_at;
        }
        return {
          ...item,
          start_time: item.start_time || item.started_at, // Prefer start_time, fallback to started_at
          end_time: effectiveEndTime, // Use the determined effectiveEndTime
          duration_seconds: item.duration_seconds, // Ensure duration_seconds is present
        };
      });
      setHistory(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch process history');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, []);

  const handleRefresh = () => {
    fetchHistory();
  };

  const handleViewLogs = (taskId: string) => {
    setSelectedTaskId(taskId);
  };

  const handleCloseLogViewer = () => {
    setSelectedTaskId(null);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'warning';
      default:
        return 'default';
    }
  };
  const formatTimestamp = (timestamp: string) => {
    // Add a check for invalid or missing timestamp
    if (!timestamp || isNaN(new Date(timestamp).getTime())) {
      console.warn(`ProcessHistory: Invalid timestamp received: ${timestamp}`);
      return 'Invalid Date';
    }
    return format(new Date(timestamp), 'PPpp');
  };

  const downloadHistoryReport = () => {
    const csvContent = [
      ['Task ID', 'Process Type', 'Status', 'Started', 'Completed', 'Duration', 'Parameters'],
      ...history.map(item => [
        item.task_id,
        item.process_type,
        item.status,
        formatTimestamp(item.start_time), /* Use start_time */
        item.end_time ? formatTimestamp(item.end_time) : 'N/A', /* Use end_time */
        calculateDuration({ startTime: item.start_time, endTime: item.end_time, status: item.status }), /* Use start_time and endTime */
        JSON.stringify(item.parameters)
      ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `process_history_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading process history...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={handleRefresh}>
          Retry
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  const paginatedHistory = history.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5" component="h2">
            Process History
          </Typography>
          <Box display="flex" gap={1}>
            <Tooltip title="Download CSV Report">
              <Button
                variant="outlined"
                startIcon={<GetApp />}
                onClick={downloadHistoryReport}
                disabled={history.length === 0}
              >
                Export
              </Button>
            </Tooltip>
            <Tooltip title="Refresh">
              <IconButton onClick={handleRefresh}>
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {history.length === 0 ? (
          <Alert severity="info">
            No completed processes found in history.
          </Alert>
        ) : (
          <>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Task ID</TableCell>
                    <TableCell>Process Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Started</TableCell>
                    <TableCell>Duration</TableCell>
                    <TableCell>Message</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedHistory.map((item) => (
                    <TableRow key={item.task_id} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {item.task_id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={item.process_type.replace(/_/g, ' ').toUpperCase()}
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={item.status.toUpperCase()}
                          color={getStatusColor(item.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatTimestamp(item.start_time)} {/* Use start_time */}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {calculateDuration({ startTime: item.start_time, endTime: item.end_time, status: item.status, durationSeconds: item.duration_seconds })} {/* Use start_time, endTime, and durationSeconds */}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                          {item.error ? (
                            <span style={{ color: 'red' }}>{item.error}</span>
                          ) : (
                            item.message || 'N/A'
                          )}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Logs">
                          <IconButton
                            size="small"
                            onClick={() => handleViewLogs(item.task_id)}
                          >
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={history.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}

        {/* Log Viewer Dialog */}
        <Dialog
          open={!!selectedTaskId}
          onClose={handleCloseLogViewer}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: { height: '80vh' }
          }}
        >
          <DialogTitle>
            Process Logs
          </DialogTitle>
          <DialogContent sx={{ p: 0 }}>
            {selectedTaskId && (
              <LogViewer
                taskId={selectedTaskId}
                onClose={handleCloseLogViewer}
              />
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default ProcessHistory;
