from bs4 import BeautifulSoup
from datetime import datetime
import os

# Import your parser class
def import_parser():
    import sys
    sys.path.append(os.path.dirname(__file__))
    from hkex_parser import HKEXReportParser
    return HKEXReportParser

# Path to the problematic file
file_path = r"C:\output\MaxPain\hkex\htio250718.htm"

if not os.path.exists(file_path):
    print(f"File not found: {file_path}")
    exit(1)

with open(file_path, 'rb') as f:
    content = f.read()

soup = BeautifulSoup(content, 'html.parser')
li = soup.prettify().split('\n')

print("\n==== First 50 lines of prettified HTML ====")
for i, line in enumerate(li[:50]):
    print(f"{i:02d}: {line}")

# Try both date extraction methods
def try_extract_trading_dates(li):
    parser_cls = import_parser()
    parser = parser_cls()
    print("\nTrying _extract_trading_dates:")
    result = parser._extract_trading_dates(li)
    print(f"Result: {result}")
    print("\nTrying _extract_hti_trading_date:")
    result2 = parser._extract_hti_trading_date(li)
    print(f"Result: {result2}")

try_extract_trading_dates(li)
