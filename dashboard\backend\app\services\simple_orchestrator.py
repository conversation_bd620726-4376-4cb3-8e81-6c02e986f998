# Simple Process Orchestrator
print("DEBUG: Starting simple_orchestrator.py")
import asyncio
import logging
import subprocess
import json
import os
import sys
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from pathlib import Path
import aiofiles  # New import for async file handling
from .script_config_service import script_config_service

# Database imports
from sqlalchemy.orm import Session
from core.database import SessionLocal
from models.dashboard import ProcessExecution
from .process_history_service import ProcessHistoryService

# Fix Windows event loop for subprocess support
if sys.platform == 'win32':
    try:
        # Set the event loop policy to ProactorEventLoop on Windows
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("DEBUG: Set Windows ProactorEventLoop policy for subprocess support")

        # Force creation of a new ProactorEventLoop if we're not already using one
        try:
            current_loop = asyncio.get_event_loop()
            if not isinstance(current_loop, asyncio.ProactorEventLoop):
                print("DEBUG: Current loop is not ProactorEventLoop, creating new one")
                new_loop = asyncio.ProactorEventLoop()
                asyncio.set_event_loop(new_loop)
                print("DEBUG: Successfully set new ProactorEventLoop")
        except RuntimeError:
            # No event loop running, which is fine
            print("DEBUG: No event loop running yet, policy will take effect when one is created")
    except Exception as e:
        print(f"DEBUG: Warning - Could not set ProactorEventLoop policy: {e}")

print("DEBUG: Imports completed")
logger = logging.getLogger(__name__)
print("DEBUG: Logger created")

class ProcessOrchestratorService:
    """Simple orchestrator for HKEX processing scripts."""

    def __init__(self):
        self.active_processes = {}
        self.websocket_manager = None

        # Ensure proper event loop for Windows subprocess support
        self._ensure_windows_event_loop()

        # Log current event loop information for debugging
        try:
            loop = asyncio.get_event_loop()
            logger.info(f"Current event loop: {type(loop)}")
            print(f"DEBUG: Current event loop: {type(loop)}")
        except Exception as e:
            logger.warning(f"Could not get current event loop: {e}")
            print(f"DEBUG: Could not get current event loop: {e}")

        # Get the root directory (where the HKEX scripts are located)
        # self.scripts_dir = Path(__file__).parent.parent.parent.parent.parent  # Go up to MaxPain2024
        # New scripts_dir to point to the top-level 'scripts' folder
        self.scripts_dir = Path("/workspace/scripts") # Corrected path
        logger.info(f"Scripts directory: {self.scripts_dir}")

        # Determine the correct Python executable
        # In a Docker container, 'python' or 'python3' should be in PATH
        self.python_executable = "python" # Default to 'python'
        # One could add a check here if needed, e.g., shutil.which('python3') or shutil.which('python')
        # For now, assume 'python' is correct within the Docker env based on python:3.13.2-slim
        logger.info(f"Using Python executable: {self.python_executable}")

        # Script configurations are now loaded dynamically from JSON file
        # via script_config_service

    def set_websocket_manager(self, manager):
        """Set WebSocket manager for broadcasting updates."""
        self.websocket_manager = manager

    async def get_process_types(self):
        """Get all available process types."""
        return await script_config_service.get_process_types()

    async def start_process(self, process_type: str, parameters: Dict[str, Any]) -> str:
        """Start a new process and return task ID."""
        config = await script_config_service.get_script(process_type)
        if not config:
            raise ValueError(f"Unknown process type: {process_type}")

        task_id = f"{process_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Validate required parameters
        for param in config['requires_params']:
            if param not in parameters:
                raise ValueError(f"Required parameter '{param}' missing for {process_type}")

        # Store process info
        now = datetime.now(timezone.utc)
        process_info = {
            'task_id': task_id,
            'process_type': process_type,
            'status': 'starting',
            'parameters': parameters,
            'started_at': now,
            'updated_at': now, # Initialize updated_at
            'end_time': None, # Initialize end_time
            'progress': 0,
            'message': 'Process initialized',
            'output': [],
            'error': None
        }

        self.active_processes[task_id] = {
            'info': process_info,
            'process': None
        }

        # Create database record for persistence
        try:
            with SessionLocal() as db:
                script_path = str(self.scripts_dir / config['script'])
                ProcessHistoryService.create_process_execution(
                    task_id=task_id,
                    process_name=process_type,
                    script_path=script_path,
                    parameters=parameters,
                    db=db
                )
                logger.info(f"Created database record for process {task_id}")
        except Exception as e:
            logger.error(f"Failed to create database record for process {task_id}: {e}")
            # Continue anyway - don't fail the process start due to DB issues

        # Start the actual process
        asyncio.create_task(self._execute_process(task_id, process_type, parameters))

        logger.info(f"Started process {task_id} of type {process_type}")
        return task_id

    async def _execute_process(self, task_id: str, process_type: str, parameters: Dict[str, Any]):
        """Execute the actual script process."""
        try:
            config = await script_config_service.get_script(process_type)
            if not config:
                raise ValueError(f"Unknown process type: {process_type}")
            script_path = self.scripts_dir / config['script']

            # Check if script exists
            if not script_path.exists():
                raise FileNotFoundError(f"Script not found: {script_path}")

            # Update status to running
            await self._update_process_status(task_id, 'running', 'Executing script...', 10)

            # Define log file paths
            log_dir = Path("/app/logs")
            log_dir.mkdir(parents=True, exist_ok=True) # Ensure log directory exists
            stdout_log_file = log_dir / f"{task_id}_stdout.log"
            stderr_log_file = log_dir / f"{task_id}_stderr.log"

            # Determine the Python executable path
            # Using self.python_executable which is set to "python" in __init__
            python_executable = self.python_executable

            # Build command arguments
            cmd_args = [python_executable, "-u", str(script_path)]

            # Add parameters dynamically based on configuration
            await self._add_dynamic_parameters(cmd_args, config, parameters)

            logger.info(f"Executing command: {' '.join(cmd_args)}")

            # Log event loop information before subprocess creation
            try:
                current_loop = asyncio.get_running_loop()
                logger.info(f"Current event loop before subprocess: {type(current_loop)}")
                if hasattr(current_loop, '_selector'):
                    logger.info(f"Event loop selector: {type(current_loop._selector)}")

                # Check if we have the right event loop for Windows subprocess support
                if sys.platform == 'win32' and not isinstance(current_loop, asyncio.ProactorEventLoop):
                    logger.warning("Windows detected but not using ProactorEventLoop - subprocess may fail")

            except Exception as e:
                logger.warning(f"Could not get event loop info: {e}")
              # Execute the process with enhanced Windows handling
            try:
                if sys.platform == 'win32':
                    # Check if we have ProactorEventLoop
                    current_loop = asyncio.get_running_loop()
                    if not isinstance(current_loop, asyncio.ProactorEventLoop):
                        logger.warning("Windows: Not using ProactorEventLoop, trying subprocess creation anyway")

                    # Try ProactorEventLoop-compatible creation first
                    try:
                        with open(stdout_log_file, 'wb') as stdout_f, open(stderr_log_file, 'wb') as stderr_f:
                            process = await asyncio.create_subprocess_exec(
                                *cmd_args,
                                stdout=stdout_f, # Redirect stdout to file
                                stderr=stderr_f, # Redirect stderr to file
                                cwd=self.scripts_dir,
                                creationflags=subprocess.CREATE_NO_WINDOW
                            )
                        logger.info(f"Successfully created subprocess with exec for task {task_id}")
                    except Exception as exec_error:
                        logger.warning(f"Exec method failed: {exec_error}, trying shell method")
                        # Fallback to shell method
                        with open(stdout_log_file, 'wb') as stdout_f, open(stderr_log_file, 'wb') as stderr_f:
                            process = await asyncio.create_subprocess_shell(
                                ' '.join(f'"{arg}"' if ' ' in arg else arg for arg in cmd_args),
                                stdout=stdout_f, # Redirect stdout to file
                                stderr=stderr_f, # Redirect stderr to file
                                cwd=self.scripts_dir
                            )
                        logger.info(f"Successfully created subprocess with shell for task {task_id}")
                else:
                    # Unix/Linux subprocess creation
                    with open(stdout_log_file, 'wb') as stdout_f, open(stderr_log_file, 'wb') as stderr_f:
                        process = await asyncio.create_subprocess_exec(
                            *cmd_args,
                            stdout=stdout_f, # Redirect stdout to file
                            stderr=stderr_f, # Redirect stderr to file
                            cwd=self.scripts_dir
                        )
                    logger.info(f"Successfully created subprocess for task {task_id}")

            except Exception as subprocess_error:
                logger.error(f"All subprocess creation methods failed: {subprocess_error}")
                # Last resort: try using threading with sync subprocess
                if sys.platform == 'win32':
                    logger.info("Attempting threading-based subprocess creation as final fallback...")
                    await self._create_subprocess_with_threading(task_id, cmd_args)
                    return
                else:
                    raise

            self.active_processes[task_id]['process'] = process
            await self._update_process_status(task_id, 'running', 'Script executing...', 25)

            # Asynchronously tail log files and send updates
            asyncio.create_task(self._tail_log_files(task_id, stdout_log_file, stderr_log_file))

            # Wait for process to complete
            await process.wait() # Simpler wait as communicate() is not needed for file-redirected stdio

            # Process completed
            if process.returncode == 0:
                await self._update_process_status(task_id, 'completed', f'Process completed successfully. Logs: {stdout_log_file}, {stderr_log_file}', 100)
                logger.info(f"Process {task_id} completed successfully. Logs: {stdout_log_file}, {stderr_log_file}")
            else:
                # Attempt to read the first few lines of stderr for a concise error message
                error_summary = ""
                try:
                    with open(stderr_log_file, 'r', encoding='utf-8', errors='replace') as err_f:
                        for _ in range(5): # Read up to 5 lines
                            line = err_f.readline()
                            if not line:
                                break
                            error_summary += line
                    if not error_summary:
                        error_summary = "Unknown error (see stderr log)."
                except Exception:
                    error_summary = "Could not read stderr log."

                error_msg = f'Process failed. Stderr: {error_summary.strip()} (Full logs: {stderr_log_file})'
                await self._update_process_status(task_id, 'failed', error_msg, 0)
                self.active_processes[task_id]['info']['error'] = error_msg
                logger.error(f"Process {task_id} failed. Stderr log: {stderr_log_file}")

        except Exception as e:
            error_msg = str(e)
            await self._update_process_status(task_id, 'failed', f'Execution error: {error_msg}', 0)
            self.active_processes[task_id]['info']['error'] = error_msg
            logger.error(f"Process {task_id} execution error: {error_msg}")

    async def get_full_log_content(self, task_id: str) -> Dict[str, Any]:
        """
        Retrieves the full log content for a given task_id.
        Reads both stdout and stderr log files and combines them.
        """
        if task_id not in self.active_processes:  # Corrected: Use self.active_processes
            logger.error(f"Task ID {task_id} not found in active_processes for full log retrieval.")
            return {"content": f"Error: Task ID {task_id} not found."}

        # Construct log file paths consistently
        log_dir = Path("/app/logs")
        stdout_log_path = log_dir / f"{task_id}_stdout.log"
        stderr_log_path = log_dir / f"{task_id}_stderr.log"

        full_log_lines = []

        try:
            if stdout_log_path.exists(): # Use Path object's exists() method
                async with aiofiles.open(stdout_log_path, mode='r', encoding='utf-8', errors='ignore') as f: # Added errors='ignore'
                    lines = await f.readlines()
                    full_log_lines.extend([f"[STDOUT] {line.strip()}" for line in lines])
            else:
                logger.warning(f"Stdout log file not found: {stdout_log_path}")
                full_log_lines.append(f"[SYSTEM] Stdout log file not found: {stdout_log_path}")
        except Exception as e:
            logger.error(f"Error reading stdout log {stdout_log_path}: {e}")
            full_log_lines.append(f"[SYSTEM] Error reading stdout log {stdout_log_path}: {e}")

        try:
            if stderr_log_path.exists(): # Use Path object's exists() method
                async with aiofiles.open(stderr_log_path, mode='r', encoding='utf-8', errors='ignore') as f: # Added errors='ignore'
                    lines = await f.readlines()
                    full_log_lines.extend([f"[STDERR] {line.strip()}" for line in lines])
            else:
                logger.warning(f"Stderr log file not found: {stderr_log_path}")
                full_log_lines.append(f"[SYSTEM] Stderr log file not found: {stderr_log_path}")
        except Exception as e:
            logger.error(f"Error reading stderr log {stderr_log_path}: {e}")
            full_log_lines.append(f"[SYSTEM] Error reading stderr log {stderr_log_path}: {e}")

        if not full_log_lines:
            return {"content": "Log files are empty or not found."}

        return {"content": "\\n".join(full_log_lines)}

    async def _tail_log_files(self, task_id: str, stdout_log_file: Path, stderr_log_file: Path):
        """Tails the stdout and stderr log files and sends updates via WebSocket."""
        try:
            stdout_pos = 0
            stderr_pos = 0
            process_active = True

            while process_active:
                process_info = self.get_process_status(task_id)
                if not process_info or process_info['status'] not in ['running', 'starting']:
                    process_active = False

                # Check if process is in terminal state - if so, don't override status
                current_status = process_info.get('status', '').lower() if process_info else ''
                is_terminal_state = current_status in ['completed', 'failed', 'cancelled', 'error']

                # Read new lines from stdout
                try:
                    with open(stdout_log_file, 'r', encoding='utf-8', errors='replace') as f_out:
                        f_out.seek(stdout_pos)
                        new_stdout_lines = f_out.readlines()
                        stdout_pos = f_out.tell()
                    for line in new_stdout_lines:
                        line_strip = line.strip()
                        if line_strip: # Avoid sending empty lines
                            # Don't override terminal status - just send log message without changing status
                            if is_terminal_state:
                                await self._send_log_message(task_id, f'[STDOUT] {line_strip}')
                            else:
                                await self._update_process_status(task_id, 'running', f'[STDOUT] {line_strip}', process_info.get('progress', 25))
                            # Also add to the 'output' field in process_info for API retrieval if needed
                            if 'output' in self.active_processes[task_id]['info'] and isinstance(self.active_processes[task_id]['info']['output'], list):
                                self.active_processes[task_id]['info']['output'].append(f'[STDOUT] {line_strip}')

                except FileNotFoundError:
                    pass # File might not be created yet
                except Exception as e:
                    logger.error(f"Error reading stdout log for {task_id}: {e}")

                # Read new lines from stderr
                try:
                    with open(stderr_log_file, 'r', encoding='utf-8', errors='replace') as f_err:
                        f_err.seek(stderr_pos)
                        new_stderr_lines = f_err.readlines()
                        stderr_pos = f_err.tell()
                    for line in new_stderr_lines:
                        line_strip = line.strip()
                        if line_strip: # Avoid sending empty lines
                            # Don't override terminal status - just send log message without changing status
                            if is_terminal_state:
                                await self._send_log_message(task_id, f'[STDERR] {line_strip}')
                            else:
                                await self._update_process_status(task_id, 'running', f'[STDERR] {line_strip}', process_info.get('progress', 25))
                            if 'output' in self.active_processes[task_id]['info'] and isinstance(self.active_processes[task_id]['info']['output'], list):
                                self.active_processes[task_id]['info']['output'].append(f'[STDERR] {line_strip}')

                except FileNotFoundError:
                    pass # File might not be created yet
                except Exception as e:
                    logger.error(f"Error reading stderr log for {task_id}: {e}")

                await asyncio.sleep(0.5) # Poll interval for log files

            logger.info(f"Stopped tailing logs for completed/failed process {task_id}")

        except Exception as e:
            logger.error(f"Error in _tail_log_files for {task_id}: {e!r}")

    async def _send_log_message(self, task_id: str, message: str):
        """Send a log message without changing process status (for terminal states)."""
        if task_id in self.active_processes:
            process_info = self.active_processes[task_id]['info']

            # Prepare payload for WebSocket broadcast with current status preserved
            process_data_for_broadcast = {
                'task_id': task_id,
                'status': process_info['status'],  # Keep current status
                'message': message,  # Just update the message
                'progress': process_info.get('progress', 100),
                'process_type': process_info['process_type'],
                'start_time': process_info['started_at'].isoformat() if process_info['started_at'] else None,
                'updated_at': datetime.now(timezone.utc).isoformat(),
            }
            if process_info.get('end_time'):
                process_data_for_broadcast['end_time'] = process_info['end_time'].isoformat()
            if process_info.get('completed_at'):
                process_data_for_broadcast['completed_at'] = process_info['completed_at'].isoformat()

            # Broadcast update via WebSocket
            if self.websocket_manager:
                await self.websocket_manager.broadcast_process_update(task_id, process_data_for_broadcast)

    def _estimate_progress(self, line: str, line_count: int) -> int:
        """Estimate progress based on output content."""
        line_lower = line.lower()

        # Basic progress estimation
        if 'starting' in line_lower or 'initializing' in line_lower:
            return 10
        elif 'connecting' in line_lower or 'login' in line_lower:
            return 20
        elif 'downloading' in line_lower or 'fetching' in line_lower:
            return 30 + min(40, line_count * 2)  # Progress during download
        elif 'processing' in line_lower or 'inserting' in line_lower:
            return 50 + min(40, line_count)  # Progress during processing
        elif 'completed' in line_lower or 'finished' in line_lower:
            return 90
        elif 'error' in line_lower or 'failed' in line_lower:
            return 0
        else:
            return min(80, 10 + line_count)  # General progress based on activity

    async def _add_dynamic_parameters(self, cmd_args: List[str], config: Dict[str, Any], parameters: Dict[str, Any]):
        """Add command-line parameters dynamically based on configuration."""
        param_mapping = config.get('param_mapping', {})

        # Process all parameters provided
        for param_name, param_value in parameters.items():
            # Skip empty or None values
            if param_value is None or param_value == '':
                continue

            # Get the command-line argument name from mapping
            cmd_arg = param_mapping.get(param_name)
            if not cmd_arg:
                # If no mapping found, use default format: --param_name
                cmd_arg = f"--{param_name.replace('_', '-')}"

            # Handle boolean parameters (flags)
            if isinstance(param_value, bool):
                if param_value:  # Only add flag if True
                    cmd_args.append(cmd_arg)
            else:
                # Handle string/numeric parameters
                cmd_args.extend([cmd_arg, str(param_value)])

        logger.info(f"Added dynamic parameters: {parameters}")

    async def _update_process_status(self, task_id: str, status: str, message: str, progress: int):
        """Update process status and broadcast via WebSocket."""
        if task_id in self.active_processes:
            process_info = self.active_processes[task_id]['info']
            # Use timezone-aware datetime to prevent timezone parsing issues
            now_utc = datetime.now(timezone.utc)

            # Normalize status to lowercase for consistency
            normalized_status = status.lower()
            process_info['status'] = normalized_status

            process_info['message'] = message
            process_info['progress'] = progress
            process_info['updated_at'] = now_utc

            # Set end_time if the process is entering a terminal state and end_time is not already set
            # Use normalized_status for comparison
            if normalized_status in ['completed', 'failed', 'cancelled', 'error'] and process_info.get('end_time') is None:
                process_info['end_time'] = now_utc
                process_info['completed_at'] = now_utc  # Ensure completed_at is set alongside end_time
                logger.info(f"Set end_time and completed_at for process {task_id} with status {normalized_status}: {now_utc.isoformat()}")

                # Update database record for terminal states
                await self._update_database_record(task_id, normalized_status, message, now_utc)

            # Prepare payload for WebSocket (this is the 'process_data' for broadcast_process_update)
            process_data_for_broadcast = {
                'task_id': task_id,  # Ensure task_id is included
                'status': normalized_status,  # Use normalized status for consistency
                'message': message,
                'progress': progress,
                'process_type': process_info['process_type'],
                'start_time': process_info['started_at'].isoformat() if process_info['started_at'] else None,
                'updated_at': process_info['updated_at'].isoformat(), # Always send updated_at
                # 'current_step': message # 'message' already covers this
            }
            if process_info.get('end_time'): # If end_time is set, send it
                process_data_for_broadcast['end_time'] = process_info['end_time'].isoformat()
                logger.info(f"Sending end_time for completed process {task_id}: {process_data_for_broadcast['end_time']}")
            if process_info.get('completed_at'): # If completed_at is set, send it
                process_data_for_broadcast['completed_at'] = process_info['completed_at'].isoformat()
                logger.info(f"Sending completed_at for completed process {task_id}: {process_data_for_broadcast['completed_at']}")

            # Broadcast update via WebSocket
            if self.websocket_manager:
                # Ensure the correct method name and arguments are used as per WebSocketManager
                await self.websocket_manager.broadcast_process_update(task_id, process_data_for_broadcast)

    async def _update_database_record(self, task_id: str, status: str, message: str, end_time: datetime):
        """Update the database record for a process in terminal state."""
        try:
            with SessionLocal() as db:
                # Read log files to get stdout/stderr content
                log_dir = Path("/app/logs")
                stdout_content = ""
                stderr_content = ""

                try:
                    stdout_log_file = log_dir / f"{task_id}_stdout.log"
                    if stdout_log_file.exists():
                        with open(stdout_log_file, 'r', encoding='utf-8', errors='replace') as f:
                            stdout_content = f.read()
                except Exception as e:
                    logger.warning(f"Could not read stdout log for {task_id}: {e}")

                try:
                    stderr_log_file = log_dir / f"{task_id}_stderr.log"
                    if stderr_log_file.exists():
                        with open(stderr_log_file, 'r', encoding='utf-8', errors='replace') as f:
                            stderr_content = f.read()
                except Exception as e:
                    logger.warning(f"Could not read stderr log for {task_id}: {e}")

                # Determine return code based on status
                return_code = 0 if status == 'completed' else 1

                ProcessHistoryService.update_process_status(
                    task_id=task_id,
                    status=status,
                    stdout=stdout_content,
                    stderr=stderr_content,
                    return_code=return_code,
                    end_time=end_time,
                    db=db
                )
                logger.info(f"Updated database record for process {task_id} with status {status}")
        except Exception as e:
            logger.error(f"Failed to update database record for process {task_id}: {e}")
            # Don't fail the process update due to DB issues

    async def cancel_process(self, task_id: str) -> bool:
        """Cancel a running process."""
        if task_id not in self.active_processes:
            return False

        self.active_processes[task_id]['info']['status'] = 'cancelled'
        logger.info(f"Cancelled process {task_id}")
        return True

    def get_process_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a process."""
        if task_id not in self.active_processes:
            return None

        return self.active_processes[task_id]['info'].copy()

    def list_active_processes(self) -> List[Dict[str, Any]]:
        """List all currently active processes."""
        return [
            data['info'].copy()
            for data in self.active_processes.values()
        ]

    def _ensure_windows_event_loop(self):
        """Ensure Windows has the correct event loop for subprocess support."""
        if sys.platform == 'win32':
            try:
                # Check current event loop
                current_loop = asyncio.get_event_loop()
                if not isinstance(current_loop, asyncio.ProactorEventLoop):
                    logger.info("Creating new ProactorEventLoop for Windows subprocess support")
                    new_loop = asyncio.ProactorEventLoop()
                    asyncio.set_event_loop(new_loop)
                    logger.info("Successfully set ProactorEventLoop")
            except RuntimeError:
                # No event loop exists yet, policy will handle it
                logger.info("No event loop running, ProactorEventLoop policy will take effect")
            except Exception as e:
                logger.warning(f"Could not ensure ProactorEventLoop: {e}")

    async def _create_subprocess_with_threading(self, task_id: str, cmd_args: List[str]):
        """Create subprocess using threading as a fallback for Windows."""
        import threading
        import queue

        logger.info(f"Using threading fallback for subprocess creation: {task_id}")

        def run_subprocess():
            """Run subprocess in a separate thread with proper encoding handling."""
            try:
                # Use traditional subprocess with threading and proper encoding
                process = subprocess.Popen(
                    cmd_args,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=self.scripts_dir,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    encoding='utf-8',  # Force UTF-8 encoding
                    errors='replace',  # Replace encoding errors instead of failing
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
                )

                # Read output line by line with encoding safety
                output_lines = []
                try:
                    for line in iter(process.stdout.readline, ''):
                        if line:
                            line_str = line.strip()
                            output_lines.append(line_str)
                            # Update process info in a thread-safe way
                            self.active_processes[task_id]['info']['output'] = output_lines[-10:]
                            logger.info(f"Process {task_id}: {line_str}")
                except UnicodeDecodeError as decode_error:
                    logger.warning(f"Encoding error reading output: {decode_error}")
                    # Continue processing despite encoding errors

                # Wait for completion
                process.wait()

                # Read stderr with encoding safety
                stderr_content = ""
                try:
                    if process.stderr:
                        stderr_content = process.stderr.read()
                except UnicodeDecodeError as decode_error:
                    logger.warning(f"Encoding error reading stderr: {decode_error}")
                    stderr_content = f"Stderr reading failed due to encoding: {decode_error}"

                return process.returncode, stderr_content

            except Exception as e:
                logger.error(f"Threading subprocess failed: {e}")
                return -1, str(e)

        # Update status to running
        await self._update_process_status(task_id, 'running', 'Executing with threading fallback...', 25)

        # Run subprocess in thread
        loop = asyncio.get_event_loop()
        try:
            returncode, stderr = await loop.run_in_executor(None, run_subprocess)

            if returncode == 0:
                await self._update_process_status(task_id, 'completed', 'Process completed successfully', 100)
                logger.info(f"Process {task_id} completed successfully via threading")
            else:
                error_msg = stderr if stderr else 'Process failed with unknown error'
                await self._update_process_status(task_id, 'failed', f'Process failed: {error_msg}', 0)
                self.active_processes[task_id]['info']['error'] = error_msg
                logger.error(f"Process {task_id} failed via threading: {error_msg}")

        except Exception as e:
            error_msg = str(e)
            await self._update_process_status(task_id, 'failed', f'Threading execution error: {error_msg}', 0)
            self.active_processes[task_id]['info']['error'] = error_msg
            logger.error(f"Process {task_id} threading execution error: {error_msg}")

    def get_log_tail(self, task_id: str, lines: int = 50) -> Dict[str, Any]:
        """Get the last N lines of process output"""
        if task_id not in self.active_processes:
            return {"error": "Process not found", "logs": []}

        process_info = self.active_processes[task_id]['info']
        output_lines = process_info.get('output', [])

        # Return the last N lines
        tail_lines = output_lines[-lines:] if len(output_lines) > lines else output_lines

        return {
            "task_id": task_id,
            "lines": len(tail_lines),
            "logs": tail_lines,
            "status": process_info.get('status', 'unknown'),
            "progress": process_info.get('progress', 0)
        }

    def get_process_logs(self, task_id: str) -> Dict[str, Any]:
        """Get detailed logs for a specific process"""
        if task_id not in self.active_processes:
            return None

        process_info = self.active_processes[task_id]['info']
        return {
            "task_id": task_id,
            "process_type": process_info.get('process_type'),
            "status": process_info.get('status'),
            "progress": process_info.get('progress', 0),
            "message": process_info.get('message'),
            "output": process_info.get('output', []),
            "error": process_info.get('error'),
            "started_at": process_info.get('started_at').isoformat() if process_info.get('started_at') else None,
            "updated_at": process_info.get('updated_at').isoformat() if process_info.get('updated_at') else None
        }

    def get_process_history(self) -> List[Dict[str, Any]]:
        """Get list of all completed processes from database history"""
        try:
            with SessionLocal() as db:
                # Get process history from database
                db_processes = ProcessHistoryService.get_process_history(db, limit=100)

                history = []
                for process in db_processes:
                    # Convert database model to dictionary format expected by frontend
                    process_dict = {
                        'task_id': process.task_id,
                        'process_type': process.process_name,
                        'status': process.status,
                        'message': f"Process {process.status}",
                        'progress': 100 if process.status == 'completed' else 0,
                        'parameters': process.parameters or {},
                        'started_at': process.start_time.isoformat() if process.start_time else None,
                        'completed_at': process.end_time.isoformat() if process.end_time else None,
                        'duration_seconds': process.duration_seconds,
                        'return_code': process.return_code,
                        'error': process.stderr if process.status == 'failed' and process.stderr else None
                    }
                    history.append(process_dict)

                logger.info(f"Retrieved {len(history)} processes from database history")
                return history

        except Exception as e:
            logger.error(f"Failed to get process history from database: {e}")
            # Fallback to in-memory history if database fails
            return self._get_in_memory_history()

    def _get_in_memory_history(self) -> List[Dict[str, Any]]:
        """Fallback method to get history from in-memory data"""
        history = []
        for process_data in self.active_processes.values():
            process_info = process_data['info'].copy()
            status = process_info.get('status', '').lower()

            if status in ['completed', 'failed', 'cancelled', 'error']:
                # Serialize datetime objects
                if process_info.get('started_at'):
                    process_info['started_at'] = process_info['started_at'].isoformat()

                if process_info.get('end_time'):
                    process_info['completed_at'] = process_info['end_time'].isoformat()
                elif process_info.get('updated_at'):
                    logger.warning(f"Process {process_info.get('task_id')} in terminal state '{status}' but missing 'end_time'. Using 'updated_at' as fallback for history.")
                    process_info['completed_at'] = process_info['updated_at'].isoformat()
                else:
                    logger.error(f"Process {process_info.get('task_id')} in terminal state '{status}' but missing 'end_time' and 'updated_at'. Setting completed_at to null for history.")
                    process_info['completed_at'] = None

                # Remove internal fields from history to keep it clean
                process_info.pop('updated_at', None)
                process_info.pop('end_time', None)

                history.append(process_info)

        history.sort(key=lambda x: x.get('started_at', ''), reverse=True)
        return history

    def get_active_processes(self) -> List[Dict[str, Any]]:
        """Get list of currently active processes with proper serialization"""
        active = []
        for task_id, process_data in self.active_processes.items():
            process_info = process_data['info'].copy()

            # Convert datetime objects to ISO strings for JSON serialization
            if 'started_at' in process_info and process_info['started_at']:
                process_info['started_at'] = process_info['started_at'].isoformat()
            if 'updated_at' in process_info and process_info['updated_at']:
                process_info['updated_at'] = process_info['updated_at'].isoformat()

            # Only include processes that are still active
            if process_info.get('status') in ['starting', 'running']:
                active.append(process_info)

        return active

    async def _execute_script(self, process_id: str, script_name: str, params: Optional[Dict[str, Any]] = None):
        """Execute a script process (internal method)."""
        script_path = self.scripts_dir / script_name
        if not script_path.exists():            
            error_msg = f"Script not found: {script_path}"
            logger.error(error_msg)
            await self._update_process_status(process_id, "error", error_msg, 0)
            return

        # Construct the command
        # Use the determined python_executable
        command = [self.python_executable, "-u", str(script_path)] # Added -u for unbuffered output

        # Add parameters if provided
        if params:
            for key, value in params.items():
                if value is True:
                    command.append(f"--{key}")
                elif value is False:
                    continue
                else:
                    command.extend([f"--{key}", str(value)])

        logger.info(f"Executing script with command: {' '.join(command)}")        # Update status to running
        await self._update_process_status(process_id, "running", "Starting script execution...", 10)

        try:
            # Execute the script process
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.scripts_dir
            )

            # Read output and error streams
            async def read_stream(stream, log_prefix):
                while True:
                    line = await stream.readline()
                    if not line:
                        break
                    line = line.decode().strip()
                    logger.info(f"{log_prefix}: {line}")

            # Launch tasks to read stdout and stderr
            await asyncio.gather(
                read_stream(process.stdout, "STDOUT"),
                read_stream(process.stderr, "STDERR")
            )

            # Wait for the process to complete
            await process.wait()            # Check the return code
            if process.returncode == 0:
                await self._update_process_status(process_id, "completed", "Script executed successfully", 100)
                logger.info(f"Process {process_id} completed successfully")
            else:
                await self._update_process_status(process_id, "failed", f"Script exited with code {process.returncode}", 100)
                logger.error(f"Process {process_id} failed with exit code {process.returncode}")

        except Exception as e:
            error_msg = f"Error executing script: {str(e)}"
            await self._update_process_status(process_id, "error", error_msg, 0)
            logger.error(f"Process {process_id} execution error: {error_msg}")

# Global instance
print("DEBUG: About to create orchestrator instance")
orchestrator = ProcessOrchestratorService()
print("DEBUG: Orchestrator created successfully")
