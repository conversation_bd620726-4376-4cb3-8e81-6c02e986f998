// WebSocket URL configuration based on environment
export const getWebSocketUrl = (): string => {
  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Use environment variable if available
  if (process.env.REACT_APP_WS_URL) {
    return process.env.REACT_APP_WS_URL;
  }
  
  // Default URLs based on environment with fallback ports
  if (isDevelopment) {
    // Development: Direct connection to backend (fallback to 8004)
    const backendPort = process.env.REACT_APP_BACKEND_PORT || '8004';
    return `ws://localhost:${backendPort}/ws`;
  } else {
    // Production: Through nginx proxy (fallback to 3080)
    const frontendPort = process.env.REACT_APP_FRONTEND_PORT || '3080';
    return `ws://localhost:${frontendPort}/ws`;
  }
};

// API URL configuration
export const getApiUrl = (): string => {
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }
  
  const isDevelopment = process.env.NODE_ENV === 'development';
  if (isDevelopment) {
    // Development: Direct connection to backend (fallback to 8004)
    const backendPort = process.env.REACT_APP_BACKEND_PORT || '8004';
    return `http://localhost:${backendPort}`;
  } else {
    // Production: Through nginx proxy (empty string means relative URLs)
    return '';
  }
};

// Environment detection utility
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

// Favicon configuration based on environment
export const getFaviconPath = (): string => {
  return isDevelopment() ? '/favicon-dev.ico' : '/favicon-prod.ico';
};

// Dynamic favicon loader utility
export const updateFavicon = (): void => {
  const faviconPath = getFaviconPath();
  
  // Find existing favicon link element
  let faviconLink = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
  
  if (!faviconLink) {
    // Create favicon link if it doesn't exist
    faviconLink = document.createElement('link');
    faviconLink.rel = 'icon';
    faviconLink.type = 'image/x-icon';
    document.head.appendChild(faviconLink);
  }
  
  // Update favicon href
  faviconLink.href = faviconPath;
  
  // Also update any apple-touch-icon if needed
  const appleTouchIcon = document.querySelector('link[rel="apple-touch-icon"]') as HTMLLinkElement;
  if (appleTouchIcon) {
    const applePath = isDevelopment() ? '/logo192-dev.png' : '/logo192-prod.png';
    appleTouchIcon.href = applePath;
  }
};
