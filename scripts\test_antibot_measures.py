"""
Test script for enhanced anti-bot HKEX fetcher
"""
import datetime as dt
from hkex_fetcher import (
    safe_http_get_with_firecrawl_fallback, 
    _http11_forced_get, 
    selenium_stealth_get,
    enhanced_http_get
)

def test_anti_bot_measures():
    """Test the new anti-bot measures against the problematic URL"""
    
    # Use the same URL that was failing
    test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250718.htm"
    
    print("🔍 TESTING ENHANCED ANTI-BOT MEASURES")
    print("=" * 60)
    print(f"Target URL: {test_url}")
    print()
    
    # Test 1: HTTP/1.1 forced connection
    print("1️⃣ Testing HTTP/1.1 forced connection...")
    print("-" * 40)
    response1 = _http11_forced_get(test_url, timeout=30)
    if response1 and response1.status_code == 200:
        content_sample = response1.text[:200].replace('\n', ' ')
        if 'ERR_HTTP2_PROTOCOL_ERROR' in response1.text:
            print("❌ Still getting HTTP/2 protocol error")
        elif 'trading day' in response1.text.lower() or 'hong kong' in response1.text.lower():
            print("✅ SUCCESS: Real HKEX content detected!")
            print(f"Content sample: {content_sample}...")
        else:
            print("⚠️ Got 200 response but content unclear")
            print(f"Content sample: {content_sample}...")
    else:
        print(f"❌ HTTP/1.1 failed: {response1.status_code if response1 else 'No response'}")
    
    print()
    
    # Test 2: Enhanced HTTP with stealth
    print("2️⃣ Testing enhanced HTTP with stealth...")
    print("-" * 40)
    response2 = enhanced_http_get(test_url, timeout=30)
    if response2 and response2.status_code == 200:
        content_sample = response2.text[:200].replace('\n', ' ')
        if 'ERR_HTTP2_PROTOCOL_ERROR' in response2.text:
            print("❌ Still getting HTTP/2 protocol error")
        elif 'trading day' in response2.text.lower() or 'hong kong' in response2.text.lower():
            print("✅ SUCCESS: Real HKEX content detected!")
            print(f"Content sample: {content_sample}...")
        else:
            print("⚠️ Got 200 response but content unclear")
            print(f"Content sample: {content_sample}...")
    else:
        print(f"❌ Enhanced HTTP failed: {response2.status_code if response2 else 'No response'}")
    
    print()
    
    # Test 3: Selenium stealth mode
    print("3️⃣ Testing Selenium stealth mode...")
    print("-" * 40)
    response3 = selenium_stealth_get(test_url, timeout=30)
    if response3 and response3.status_code == 200:
        content_sample = response3.text[:200].replace('\n', ' ')
        if 'ERR_HTTP2_PROTOCOL_ERROR' in response3.text:
            print("❌ Still getting HTTP/2 protocol error")
        elif 'trading day' in response3.text.lower() or 'hong kong' in response3.text.lower():
            print("✅ SUCCESS: Real HKEX content detected!")
            print(f"Content sample: {content_sample}...")
        else:
            print("⚠️ Got 200 response but content unclear")
            print(f"Content sample: {content_sample}...")
    else:
        print(f"❌ Selenium stealth failed: {response3.status_code if response3 else 'No response'}")
    
    print()
    
    # Test 4: Full fallback chain
    print("4️⃣ Testing full fallback chain...")
    print("-" * 40)
    response4 = safe_http_get_with_firecrawl_fallback(test_url, timeout=30)
    if response4 and response4.status_code == 200:
        content_sample = response4.text[:200].replace('\n', ' ')
        if 'ERR_HTTP2_PROTOCOL_ERROR' in response4.text:
            print("❌ Still getting HTTP/2 protocol error")
        elif 'trading day' in response4.text.lower() or 'hong kong' in response4.text.lower():
            print("✅ SUCCESS: Real HKEX content detected!")
            print(f"Content sample: {content_sample}...")
        else:
            print("⚠️ Got 200 response but content unclear")
            print(f"Content sample: {content_sample}...")
    else:
        print(f"❌ Full fallback failed: {response4.status_code if response4 else 'No response'}")
    
    print()
    print("🎯 SUMMARY:")
    print("- If any method shows 'Real HKEX content detected', the anti-bot measures worked!")
    print("- If all still show HTTP/2 errors, HKEX may have stronger protection")
    print("- Try running at different times or with VPN if all methods fail")

if __name__ == "__main__":
    test_anti_bot_measures()
