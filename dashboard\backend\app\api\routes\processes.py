from fastapi import APIRouter, HTTPException, Query
from services.simple_orchestrator import orchestrator
from models.schemas import ProcessStartRequest
from pydantic import BaseModel
from typing import Optional, List
import logging
import asyncio # Added

router = APIRouter()
logger = logging.getLogger(__name__) # Added

class ProcessResponse(BaseModel):
    task_id: str
    message: str

@router.post("/start")
async def start_process(request: ProcessStartRequest):
    """Start a data processing script"""
    try:
        # Validate process type against dynamic configuration
        from services.script_config_service import script_config_service
        is_valid = await script_config_service.validate_process_type(request.process)
        if not is_valid:
            valid_types = await script_config_service.get_valid_process_types()
            raise HTTPException(status_code=400, detail=f"Invalid process type '{request.process}'. Valid types: {valid_types}")

        # Process type is already a string, no conversion needed
        task_id = await orchestrator.start_process(request.process, request.parameters)
        return {"task_id": task_id, "message": "Process started successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start process: {str(e)}")

@router.get("/{task_id}/status")
async def get_process_status(task_id: str):
    """Get the status of a running process"""
    try:
        status = orchestrator.get_process_status(task_id)
        if status:
            return status
        else:
            raise HTTPException(status_code=404, detail="Process not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get process status: {str(e)}")

@router.post("/{task_id}/cancel")
async def cancel_process(task_id: str):
    """Cancel a running process"""
    try:
        success = await orchestrator.cancel_process(task_id)
        if success:
            return {"message": "Process cancelled successfully"}
        else:
            raise HTTPException(status_code=404, detail="Process not found or already completed")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel process: {str(e)}")

@router.get("/active")
async def get_active_processes():
    """Get list of all active processes"""
    try:
        return orchestrator.get_active_processes()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get active processes: {str(e)}")

@router.get("/types")
async def get_process_types():
    """Get available process types"""
    try:
        # Get configurations from orchestrator (now async)
        orchestrator_types = await orchestrator.get_process_types()

        # Convert to frontend format
        process_types = []
        for key, config in orchestrator_types.items():
            process_types.append({
                "value": key,
                "label": config['description'],
                "description": config['description'],
                "required_parameters": config.get('requires_params', []),
                "optional_parameters": config.get('optional_params', [])
            })

        return {
            "process_types": process_types,
            "configurations": orchestrator_types
        }
    except Exception as e:
        import traceback
        print(f"Error in get_process_types: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get process types: {str(e)}")

@router.get("/history")
async def get_process_history():
    """Get list of all completed processes from history"""
    try:
        return orchestrator.get_process_history()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get process history: {str(e)}")

@router.get("/{task_id}/log-tail")
async def get_process_log_tail(task_id: str, lines: int = 50):
    """Get the last N lines of a process log for real-time viewing"""
    try:
        log_tail = orchestrator.get_log_tail(task_id, lines)
        return log_tail
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get log tail: {str(e)}")

@router.get("/{task_id}/log-full")
async def get_process_full_logs(task_id: str):
    """Get the complete log content for a process"""
    try:
        logger.info(f"Attempting to get full logs for task: {task_id}")

        # Debug: Log type of the method itself
        method_to_call = orchestrator.get_full_log_content
        logger.info(f"Type of orchestrator.get_full_log_content: {type(method_to_call)}")

        # Debug: Log type of what the method call returns *before* await
        # This will execute the method and store its immediate return (which should be a coroutine)
        returned_object = method_to_call(task_id)
        logger.info(f"Type of object returned by orchestrator.get_full_log_content(task_id): {type(returned_object)}")
        logger.info(f"Is it a coroutine? {asyncio.iscoroutine(returned_object)}")

        full_logs = await returned_object # Await the (hopefully) coroutine object
        logger.info(f"Successfully awaited and got full_logs. Type: {type(full_logs)}")
        return full_logs
    except Exception as e:
        logger.error(f"Error in get_process_full_logs for task {task_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get full logs: {str(e)}")

@router.get("/{task_id}/logs")
async def get_process_logs(task_id: str):
    """Get detailed logs for a specific process"""
    try:
        logs = orchestrator.get_process_logs(task_id)
        if logs:
            return logs
        else:
            raise HTTPException(status_code=404, detail="Process logs not found")
    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get process logs: {str(e)}")
