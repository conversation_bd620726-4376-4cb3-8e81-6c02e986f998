# Orphaned Process and Real-Time Log Fixes

## Issues Addressed

### 1. Log Content Height Issue ✅ FIXED
**Problem**: Log content box was overshooting page height due to components above it.
**Solution**: Increased height offset from 120px to 280px to compensate for header and control components.

**File**: `dashboard/frontend/src/components/RealTimeLogViewer.tsx`
**Change**: Line 280 - `height: isFullscreen ? '100vh' : 'calc(100vh - 280px)'`

### 2. Orphaned Process Issue ✅ FIXED
**Problem**: When backend server restarts, running processes become orphaned in database (stuck in "running" state) but are no longer tracked in memory.

**Root Cause**: 
- `active_processes` dictionary is in-memory only
- Database records persist but lose connection to actual processes
- No cleanup mechanism for orphaned processes

**Solutions Implemented**:

#### A. Automatic Startup Cleanup
**File**: `dashboard/backend/app/services/simple_orchestrator.py`
- Added `_startup_initialization()` method that runs on server startup
- Automatically calls `cleanup_orphaned_processes()` during initialization
- Logs cleanup results for monitoring

#### B. Manual Cleanup Endpoint
**File**: `dashboard/backend/app/api/routes/processes.py`
- Added `POST /api/v1/processes/cleanup-orphaned` endpoint
- Allows manual triggering of orphaned process cleanup

#### C. Frontend Auto-Cleanup
**File**: `dashboard/frontend/src/components/ProcessHistory.tsx`
- Modified `fetchHistory()` to automatically trigger cleanup when refreshing
- Ensures orphaned processes are cleaned up when user views process history

#### D. Smart Orphan Detection
**File**: `dashboard/backend/app/services/simple_orchestrator.py`
- Added `cleanup_orphaned_processes()` method with intelligent detection:
  - Finds processes marked as "running" or "pending" in database
  - Checks if they exist in `active_processes` memory
  - Uses 5-minute threshold to avoid marking recent processes as orphaned
  - Marks truly orphaned processes as "failed" with descriptive error message

### 3. Real-Time Log Disconnection Issue ✅ IMPROVED
**Problem**: After server restart, real-time logs become unavailable even for processes that were running.

**Root Cause**:
- Log files may be lost or inaccessible after restart
- No fallback mechanism for disconnected real-time logs

**Solution Implemented**:
**File**: `dashboard/frontend/src/components/RealTimeLogViewer.tsx`
- Enhanced `fetchLogTail()` with robust fallback mechanism:
  1. First attempts real-time log tail endpoint
  2. If that fails, automatically tries full log endpoint as fallback
  3. Processes full log content and extracts last N lines
  4. Shows clear user notification when using archived logs
  5. Graceful error handling with informative messages

## Technical Implementation Details

### Orphaned Process Cleanup Logic
```typescript
// Frontend triggers cleanup on history refresh
await fetch('/api/v1/processes/cleanup-orphaned', { method: 'POST' });

// Backend identifies orphaned processes
for (process in active_db_processes) {
    if (process.task_id not in active_processes && 
        process.updated_at < 5_minutes_ago) {
        mark_as_failed(process, "Process orphaned after server restart");
    }
}
```

### Log Fallback Mechanism
```typescript
// Try real-time logs first
let response = await fetch(`/api/v1/processes/${taskId}/log-tail`);

if (!response.ok) {
    // Fallback to full logs
    response = await fetch(`/api/v1/processes/${taskId}/log-full`);
    const fullContent = response.json().content;
    const tailLines = fullContent.split('\n').slice(-maxLines);
    setLogs(tailLines);
    setError('Note: Showing archived logs (real-time logs unavailable)');
}
```

## Benefits

1. **Automatic Recovery**: Server restarts no longer leave orphaned processes
2. **User Transparency**: Clear indication when processes are orphaned or logs are archived
3. **Robust Logging**: Fallback mechanism ensures logs are always accessible when possible
4. **Proactive Cleanup**: Both automatic and manual cleanup options
5. **Better UX**: Proper height management for log content display

## Testing Recommendations

1. **Orphaned Process Test**:
   - Start a long-running process
   - Restart backend server
   - Refresh ProcessHistory tab
   - Verify orphaned process is marked as "failed"

2. **Log Fallback Test**:
   - Start a process and view real-time logs
   - Restart backend server
   - Try to view logs again
   - Verify fallback to archived logs works

3. **Height Test**:
   - Open RealTimeLogViewer
   - Resize browser window
   - Verify log content uses full available height without overflow

## Future Enhancements

1. **Process Recovery**: Attempt to reconnect to still-running processes after restart
2. **Log Persistence**: Implement more robust log storage and retrieval
3. **Health Monitoring**: Add process health checks and automatic recovery
4. **Notification System**: Alert users when processes become orphaned
